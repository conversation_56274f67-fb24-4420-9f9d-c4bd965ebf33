import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import { message, Input, Button, Spin } from 'antd';
import moment from 'moment';
import traceApi from '@/service/traceApi';
import type { TraceHandleParams, ApiResponse } from '@/service/traceApi';
import type { Form } from '@formily/core/esm/models';
import style from '../traceability.module.less';

type PropsTypes = {
  dataObj: Record<string, unknown>;
  closeModal: () => void;
  /** handle:处理、view:查看 */
  modalType: 'handle' | 'view';
};

/**
 * @description 溯源记录 查看、处理弹窗
 * @returns
 */
const TraceabilityModal: React.FC<PropsTypes> = ({ dataObj, closeModal = () => {}, modalType }) => {
  const { TextArea } = Input;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);

  useEffect(() => {
    if (dataObj && dataObj.id && dataObj.id !== '') {
      form.setValues({
        ...dataObj,
        id: dataObj.id,
        handleTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      });
    } else {
      message.error('数据出错').then(() => {
        closeModal();
      });
    }
  }, [dataObj, form, closeModal]);

  const cancel: () => void = () => {
    form.reset();
    closeModal();
  };

  const submitHandleData: (data: TraceHandleParams) => Promise<void> = async (data) => {
    setIsLoading(true);
    try {
      const res: ApiResponse = await traceApi.handleTraceRecord(data);
      if (res && res.code && res.code === 200) {
        message.success('处理溯源记录成功');
        closeModal();
      } else {
        message.error(res.msg || '处理溯源记录失败');
      }
    } catch (error) {
      message.error('处理溯源记录失败');
    }
    setIsLoading(false);
  };

  const save: () => void = () => {
    form.validate().then(() => {
      const submitData: TraceHandleParams = {
        id: form.values.id,
        handlePsn: form.values.handlePsn,
        handleTime: form.values.handleTime,
        handleContent: form.values.handleContent,
        handleResult: form.values.handleResult,
      };
      submitHandleData(submitData);
    });
  };

  return (
    <div className={style['trace-handle-form']}>
      <Spin spinning={isLoading}>
        <YTHForm form={form}>
          <YTHForm.Item
            name="alarmId"
            title="报警ID"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="highRiskEnterpriseName"
            title="高风险企业名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="lowRiskEnterpriseName"
            title="低风险企业名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="pollutionType"
            title="污染类型"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="tracingCode"
            title="溯源指标"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="pollutantConcentration"
            title="相关报警值"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="tracingTime"
            title="溯源时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              disabled: true,
              placeholder: '',
              precision: 'second',
              formatter: 'YYYY-MM-DD HH:mm:ss',
            }}
          />

          {modalType === 'handle' && (
            <>
              <YTHForm.Item
                name="handlePsn"
                title="处理人"
                labelType={1}
                required
                componentName="Input"
                componentProps={{
                  placeholder: '请输入处理人',
                }}
              />
              <YTHForm.Item
                name="handleTime"
                title="处理时间"
                labelType={1}
                required
                componentName="DatePicker"
                componentProps={{
                  disabled: true,
                  placeholder: '',
                  precision: 'second',
                  formatter: 'YYYY-MM-DD HH:mm:ss',
                }}
              />
              <YTHForm.Item
                name="handleContent"
                title="处理内容"
                mergeRow={2}
                required
                component={TextArea}
                componentProps={{
                  placeholder: '请输入处理内容',
                  rows: 4,
                }}
              />
              <YTHForm.Item
                name="handleResult"
                title="处理结果"
                mergeRow={2}
                required
                component={TextArea}
                componentProps={{
                  placeholder: '请输入处理结果',
                  rows: 3,
                }}
              />
            </>
          )}
        </YTHForm>

        {modalType === 'handle' && (
          <div className={style['form-buttons']}>
            <Button onClick={cancel} className={style['cancel-btn']}>
              取消
            </Button>
            <Button onClick={save} className={style['save-btn']} type="primary">
              保存
            </Button>
          </div>
        )}
      </Spin>
    </div>
  );
};

export default TraceabilityModal;
